import * as React from "react"

const MOBILE_BREAKPOINT = 768

export function useIsMobile() {
  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)

  React.useEffect(() => {
    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)
    const onChange = React.useCallback(() => {
      setIsMobile(mql.matches)
    }, [mql])

    mql.addEventListener("change", onChange)
    setIsMobile(mql.matches) // Use mql.matches instead of direct window access

    return () => mql.removeEventListener("change", onChange)
  }, []) // Empty dependency array is correct here

  return !!isMobile
}
