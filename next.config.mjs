/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    // Static export requires unoptimized images
    unoptimized: true,
    dangerouslyAllowSVG: false,
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },
  // Enable experimental features for better performance
  experimental: {
    scrollRestoration: true,
    optimizeCss: true,
  },
  // Optimize bundle
  webpack: (config) => {
    // Enable tree shaking
    config.optimization.usedExports = true;
    config.optimization.sideEffects = false;
    return config;
  },
  // Compress responses
  compress: true,
  // Enable static export for deployment
  output: 'export',
  trailingSlash: true,
}

export default nextConfig
